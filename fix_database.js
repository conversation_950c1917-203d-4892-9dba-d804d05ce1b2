const mysql = require('mysql2/promise');
require('dotenv').config();

async function fixDatabase() {
  const connection = await mysql.createConnection({
    host: process.env.DB_HOST || 'localhost',
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'gg_catalog_db'
  });
  
  try {
    console.log('🔧 Starting database fixes...');
    
    // 1. Fix the price column data type in products table
    console.log('1. Updating products.price column to DECIMAL(12,2)...');
    await connection.execute('ALTER TABLE products MODIFY COLUMN price DECIMAL(12,2) DEFAULT 0');
    console.log('✅ Products price column updated');
    
    // 2. Add price column to product_variants table
    console.log('2. Adding price column to product_variants table...');
    try {
      await connection.execute('ALTER TABLE product_variants ADD COLUMN price DECIMAL(12,2) NOT NULL DEFAULT 0');
      console.log('✅ Price column added to product_variants');
    } catch (error) {
      if (error.message.includes('Duplicate column name')) {
        console.log('⚠️  Price column already exists in product_variants');
      } else {
        throw error;
      }
    }
    
    // 3. Update existing products with sample prices
    console.log('3. Setting default prices for existing products...');
    await connection.execute(`
      UPDATE products SET 
        price = CASE 
          WHEN name LIKE '%iPhone%' THEN 999.99
          WHEN name LIKE '%Nike%' THEN 129.99
          WHEN name LIKE '%Adidas%' THEN 29.99
          ELSE 19.99
        END
      WHERE price IS NULL OR price = 0
    `);
    console.log('✅ Default prices set for existing products');
    
    // 4. Update product variants with prices
    console.log('4. Setting prices for product variants...');
    await connection.execute(`
      UPDATE product_variants pv
      JOIN products p ON pv.product_id = p.id
      SET pv.price = p.price + (pv.id * 10)
      WHERE pv.price = 0
    `);
    console.log('✅ Prices set for product variants');
    
    // 5. Verify the fixes
    console.log('\n🔍 Verifying fixes...');
    
    // Check products table
    const [products] = await connection.execute('SELECT id, name, price FROM products LIMIT 5');
    console.log('Products with prices:');
    products.forEach(p => console.log(`- ID: ${p.id}, Name: ${p.name}, Price: $${p.price}`));
    
    // Check product_variants table
    const [variants] = await connection.execute(`
      SELECT pv.id, pv.variant_name, pv.price, p.name as product_name 
      FROM product_variants pv 
      JOIN products p ON pv.product_id = p.id 
      LIMIT 5
    `);
    console.log('\nProduct variants with prices:');
    variants.forEach(v => console.log(`- ${v.product_name} - ${v.variant_name}: $${v.price}`));
    
    console.log('\n🎉 Database fixes completed successfully!');
    
  } catch (error) {
    console.error('❌ Database fix failed:', error.message);
    throw error;
  } finally {
    await connection.end();
  }
}

// Run the fix
if (require.main === module) {
  fixDatabase()
    .then(() => {
      console.log('✨ All fixes applied successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Fix failed:', error.message);
      process.exit(1);
    });
}

module.exports = { fixDatabase };
