const mysql = require('mysql2/promise');
require('dotenv').config();

async function checkDatabase() {
  const connection = await mysql.createConnection({
    host: process.env.DB_HOST || 'localhost',
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'gg_catalog_db'
  });
  
  try {
    console.log('Checking products table structure...');
    const [columns] = await connection.execute('DESCRIBE products');
    console.log('Products table columns:');
    columns.forEach(col => console.log(`- ${col.Field}: ${col.Type} ${col.Null === 'YES' ? '(nullable)' : '(not null)'}`));
    
    console.log('\nChecking product_variants table structure...');
    const [variantColumns] = await connection.execute('DESCRIBE product_variants');
    console.log('Product_variants table columns:');
    variantColumns.forEach(col => console.log(`- ${col.Field}: ${col.Type} ${col.Null === 'YES' ? '(nullable)' : '(not null)'}`));
    
    console.log('\nChecking sample data...');
    const [products] = await connection.execute('SELECT id, name, price FROM products LIMIT 3');
    console.log('Sample products:');
    products.forEach(p => console.log(`- ID: ${p.id}, Name: ${p.name}, Price: ${p.price}`));
    
  } catch (error) {
    console.error('Database check failed:', error.message);
  } finally {
    await connection.end();
  }
}

checkDatabase();
